# 故障排除指南

## 问题分析

根据你提供的错误日志，主要问题是 "file does not exist"，这表明插件无法找到对应的文件。

## 可能的原因

1. **Gitea 服务器连接问题**
   - 服务器 URL 配置错误
   - 网络连接问题
   - HTTPS/HTTP 协议不匹配

2. **仓库配置问题**
   - 仓库不存在或不可访问
   - 缺少必要的分支 (gitea-pages)
   - 缺少主题标签 (gitea-pages)

3. **Token 权限问题**
   - Token 无效或过期
   - Token 没有读取权限

4. **文件路径问题**
   - 目标文件不存在
   - 路径解析错误

## 解决步骤

### 1. 运行诊断脚本

```bash
chmod +x diagnose.sh
./diagnose.sh
```

这个脚本会检查：
- Gitea 服务器连接
- API 可访问性
- 仓库存在性
- 分支存在性
- 主题标签配置

### 2. 检查 Gitea 配置

确保你的 Gitea 服务器上有：

1. **用户**: QingXue
2. **仓库**: gitea-pages
3. **分支**: gitea-pages
4. **主题标签**: gitea-pages (在仓库设置中添加)
5. **文件**: 在 gitea-pages 分支中至少有一个 index.html 文件

### 3. 验证 Token

在 Gitea 中创建一个新的 Personal Access Token：
1. 登录 Gitea
2. 进入 Settings -> Applications
3. 创建新的 Token
4. 确保有 "repo" 权限

### 4. 使用调试版本

使用 `Caddyfile.debug` 配置文件来获取更详细的日志：

```bash
# 构建调试版本
xcaddy build --with caddy-gitea-local=./

# 使用调试配置运行
./caddy run --config Caddyfile.debug
```

### 5. Docker 构建

如果你想使用 Docker：

```bash
# 构建镜像
chmod +x build_docker.sh
./build_docker.sh

# 运行容器
docker run -d -p 8098:8098 \
  -v $(pwd)/Caddyfile.debug:/etc/caddy/Caddyfile \
  caddy-gitea-local:latest
```

## 配置示例

### 正确的 Caddyfile 配置

```caddyfile
{
    order gitea before file_server
    debug
}

:8098 {
    gitea {
        server http://192.168.3.4:3000
        token your_actual_token_here
        gitea_pages gitea-pages
        gitea_pages_allowall gitea-pages-allowall
    }
    
    log {
        output file /var/log/caddy/gitea-pages.log
        format json
        level DEBUG
    }
}
```

### Gitea 仓库结构

```
QingXue/gitea-pages (仓库)
├── gitea-pages (分支)
│   ├── index.html
│   ├── about.html
│   └── assets/
│       └── style.css
└── main (分支)
    └── README.md
```

## 常见错误和解决方案

### 1. "file does not exist"
- 检查仓库和分支是否存在
- 确认文件路径正确
- 验证 Token 权限

### 2. "connection refused"
- 检查 Gitea 服务器是否运行
- 验证 IP 地址和端口
- 检查防火墙设置

### 3. "unauthorized"
- 检查 Token 是否有效
- 确认 Token 有读取权限
- 检查仓库是否为私有

## 版本信息

当前版本: v1.0.0-local

插件会在启动时打印版本信息和配置详情，查看日志以确认插件正确加载。
