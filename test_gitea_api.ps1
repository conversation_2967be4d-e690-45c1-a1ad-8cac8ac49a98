# 测试 Gitea API 连接脚本
param(
    [string]$GiteaServer = "https://***********:33000",
    [string]$Token = "your_token_here"
)

Write-Host "=== 测试 Gitea API 连接 ===" -ForegroundColor Yellow
Write-Host "服务器: $GiteaServer"
Write-Host ""

# 检查 Token
if ($Token -eq "your_token_here") {
    Write-Host "⚠ 请先设置正确的 TOKEN" -ForegroundColor Red
    Write-Host "使用方法: .\test_gitea_api.ps1 -Token 'your_actual_token'"
    exit 1
}

# 设置请求头
$headers = @{
    'Authorization' = "token $Token"
}

# 忽略 SSL 证书错误（用于自签名证书）
if ($PSVersionTable.PSVersion.Major -ge 6) {
    $PSDefaultParameterValues['Invoke-RestMethod:SkipCertificateCheck'] = $true
    $PSDefaultParameterValues['Invoke-WebRequest:SkipCertificateCheck'] = $true
} else {
    # PowerShell 5.x
    [System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12
}

# 1. 测试基本连接
Write-Host "1. 测试基本连接..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri $GiteaServer -TimeoutSec 10 -UseBasicParsing
    Write-Host "✓ 基本连接成功" -ForegroundColor Green
} catch {
    Write-Host "✗ 基本连接失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 测试 API 版本
Write-Host ""
Write-Host "2. 测试 API 版本..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "$GiteaServer/api/v1/version" -TimeoutSec 10
    Write-Host "✓ API 可访问" -ForegroundColor Green
    Write-Host "版本信息: $($response.version)"
} catch {
    Write-Host "✗ API 不可访问: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 3. 测试 Token 认证
Write-Host ""
Write-Host "3. 测试 Token 认证..." -ForegroundColor Cyan
try {
    $user = Invoke-RestMethod -Uri "$GiteaServer/api/v1/user" -Headers $headers -TimeoutSec 10
    Write-Host "✓ Token 认证成功" -ForegroundColor Green
    Write-Host "当前用户: $($user.login)"
} catch {
    Write-Host "✗ Token 认证失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 4. 测试仓库访问
Write-Host ""
Write-Host "4. 测试仓库访问..." -ForegroundColor Cyan
try {
    $repo = Invoke-RestMethod -Uri "$GiteaServer/api/v1/repos/QingXue/gitea-pages" -Headers $headers -TimeoutSec 10
    Write-Host "✓ 仓库 QingXue/gitea-pages 可访问" -ForegroundColor Green
    Write-Host "仓库名称: $($repo.name)"
    Write-Host "仓库描述: $($repo.description)"
} catch {
    Write-Host "✗ 仓库 QingXue/gitea-pages 不可访问: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请检查:"
    Write-Host "- 仓库是否存在"
    Write-Host "- Token 是否有读取权限"
    Write-Host "- 用户名是否正确"
}

# 5. 测试分支
Write-Host ""
Write-Host "5. 测试 gitea-pages 分支..." -ForegroundColor Cyan
try {
    $branch = Invoke-RestMethod -Uri "$GiteaServer/api/v1/repos/QingXue/gitea-pages/branches/gitea-pages" -Headers $headers -TimeoutSec 10
    Write-Host "✓ gitea-pages 分支存在" -ForegroundColor Green
    Write-Host "分支名称: $($branch.name)"
} catch {
    Write-Host "✗ gitea-pages 分支不存在: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 测试主题标签
Write-Host ""
Write-Host "6. 测试仓库主题标签..." -ForegroundColor Cyan
try {
    $topics = Invoke-RestMethod -Uri "$GiteaServer/api/v1/repos/QingXue/gitea-pages/topics" -Headers $headers -TimeoutSec 10
    Write-Host "主题标签: $($topics.topics -join ', ')"
    if ($topics.topics -contains 'gitea-pages') {
        Write-Host "✓ 找到 gitea-pages 主题标签" -ForegroundColor Green
    } else {
        Write-Host "⚠ 未找到 gitea-pages 主题标签，建议添加" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ 无法访问主题标签 API: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. 测试文件访问
Write-Host ""
Write-Host "7. 测试文件访问..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "$GiteaServer/api/v1/repos/QingXue/gitea-pages/media/index.html?ref=gitea-pages" -Headers $headers -TimeoutSec 10 -UseBasicParsing
    Write-Host "✓ index.html 文件可访问" -ForegroundColor Green
    Write-Host "文件大小: $($response.Content.Length) 字节"
} catch {
    Write-Host "✗ index.html 文件不可访问: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== 测试完成 ===" -ForegroundColor Yellow
Write-Host ""
Write-Host "如果所有测试都通过，请重新构建并运行 Caddy:"
Write-Host "1. 运行 build_windows.bat 重新构建"
Write-Host "2. 使用正确的 token 更新 Caddyfile"
Write-Host "3. 运行 caddy.exe run --config Caddyfile.fixed"
