#!/bin/bash

# 测试构建脚本
echo "开始测试构建 caddy-gitea-local..."

# 检查 Go 是否安装
if ! command -v go &> /dev/null; then
    echo "错误: Go 未安装。请先安装 Go 1.19 或更高版本。"
    exit 1
fi

# 检查 Go 版本
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
echo "检测到 Go 版本: $GO_VERSION"

# 清理模块缓存
echo "清理模块依赖..."
go mod tidy

# 检查语法
echo "检查代码语法..."
go vet ./...
if [ $? -ne 0 ]; then
    echo "错误: 代码语法检查失败"
    exit 1
fi

# 尝试编译
echo "尝试编译..."
go build -o test-build ./...
if [ $? -eq 0 ]; then
    echo "✓ 编译成功！"
    rm -f test-build
else
    echo "✗ 编译失败"
    exit 1
fi

# 检查 xcaddy 是否安装
if ! command -v xcaddy &> /dev/null; then
    echo "警告: xcaddy 未安装。请运行以下命令安装:"
    echo "go install github.com/caddyserver/xcaddy/cmd/xcaddy@latest"
    echo ""
    echo "然后运行以下命令构建 Caddy:"
    echo "xcaddy build --with caddy-gitea-local=./"
else
    echo "开始构建 Caddy..."
    xcaddy build --with caddy-gitea-local=./
    if [ $? -eq 0 ]; then
        echo "✓ Caddy 构建成功！"
    else
        echo "✗ Caddy 构建失败"
        exit 1
    fi
fi

echo "测试完成！"
