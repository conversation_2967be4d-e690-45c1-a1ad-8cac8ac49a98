package gitea

import (
	"bytes"
	"crypto/tls"
	"fmt"
	"io"
	"io/fs"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	gclient "code.gitea.io/sdk/gitea"
	"github.com/spf13/viper"
)

type Client struct {
	serverURL          string
	token              string
	giteapages         string
	giteapagesAllowAll string
	gc                 *gclient.Client
}

func NewClient(serverURL, token, giteapages, giteapagesAllowAll string) (*Client, error) {
	if giteapages == "" {
		giteapages = "gitea-pages"
	}

	if giteapagesAllowAll == "" {
		giteapagesAllowAll = "gitea-pages-allowall"
	}

	// 创建跳过证书验证的 HTTP 客户端
	httpClient := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	fmt.Printf("[DEBUG] Creating Gitea client with serverURL: %s\n", serverURL)
	gc, err := gclient.NewClient(serverURL, gclient.SetToken(token), gclient.SetGiteaVersion(""), gclient.SetHTTPClient(httpClient))
	if err != nil {
		fmt.Printf("[ERROR] Failed to create Gitea client: %v\n", err)
		return nil, err
	}
	fmt.Printf("[DEBUG] Gitea client created successfully\n")

	return &Client{
		serverURL:          serverURL,
		token:              token,
		gc:                 gc,
		giteapages:         giteapages,
		giteapagesAllowAll: giteapagesAllowAll,
	}, nil
}

func (c *Client) Open(name, ref string) (fs.File, error) {
	timestamp := time.Now().Format("15:04:05.000")
	fmt.Printf("\n[%s] ========== NEW REQUEST ==========\n", timestamp)
	fmt.Printf("[%s] Open called with name: %s, ref: %s\n", timestamp, name, ref)

	owner, repo, filepath := splitName(name)
	fmt.Printf("[%s] Parsed - owner: %s, repo: %s, filepath: %s\n", timestamp, owner, repo, filepath)

	// if repo is empty they want to have the gitea-pages repo
	if repo == "" {
		repo = c.giteapages
		filepath = "index.html"
	}

	// if filepath is empty they want to have the index.html
	if filepath == "" {
		filepath = "index.html"
	}

	timestamp := time.Now().Format("15:04:05.000")
	fmt.Printf("[%s] Checking repo access for: %s/%s\n", timestamp, owner, repo)

	// 对于默认的 gitea-pages 仓库，直接允许访问
	if repo == c.giteapages {
		fmt.Printf("[%s] This is the default gitea-pages repo, allowing access\n", timestamp)
		if !c.hasRepoBranch(owner, repo, c.giteapages) {
			fmt.Printf("[%s] gitea-pages branch doesn't exist\n", timestamp)
			return nil, fs.ErrNotExist
		}
	} else {
		// 对于其他仓库，检查主题标签
		limited, allowall := c.allowsPages(owner, repo)
		fmt.Printf("[%s] allowsPages result for %s/%s - limited: %t, allowall: %t\n", timestamp, owner, repo, limited, allowall)

		if !limited && !allowall {
			fmt.Printf("[%s] Repo %s/%s doesn't have gitea-pages topic, access denied\n", timestamp, owner, repo)
			return nil, fs.ErrNotExist
		}
	}

	hasConfig := true

	if err := c.readConfig(owner, repo); err != nil {
		// we don't need a config for gitea-pages
		// no config is only exposing the gitea-pages branch
		if repo != c.giteapages && !allowall {
			return nil, err
		}

		hasConfig = false
	}

	// if we don't have a config and the repo is the gitea-pages
	// always overwrite the ref to the gitea-pages branch
	if !hasConfig && (repo == c.giteapages || ref == c.giteapages) {
		ref = c.giteapages
	} else if !validRefs(ref, allowall) {
		return nil, fs.ErrNotExist
	}

	res, err := c.getRawFileOrLFS(owner, repo, filepath, ref)
	if err != nil {
		return nil, err
	}

	if strings.HasSuffix(filepath, ".md") {
		res, err = handleMD(res)
		if err != nil {
			return nil, err
		}
	}

	return &openFile{
		content: res,
		name:    filepath,
	}, nil
}

func (c *Client) getRawFileOrLFS(owner, repo, filepath, ref string) ([]byte, error) {
	var (
		giteaURL string
		err      error
	)

	// TODO: make pr for go-sdk
	// gitea sdk doesn't support "media" type for lfs/non-lfs
	giteaURL, err = url.JoinPath(c.serverURL+"/api/v1/repos/", owner, repo, "media", filepath)
	if err != nil {
		fmt.Printf("[ERROR] Failed to build URL: %v\n", err)
		return nil, err
	}

	giteaURL += "?ref=" + url.QueryEscape(ref)

	timestamp := time.Now().Format("15:04:05.000")
	fmt.Printf("[%s] ===== GITEA API REQUEST =====\n", timestamp)
	fmt.Printf("[%s] Server URL: %s\n", timestamp, c.serverURL)
	fmt.Printf("[%s] Owner: %s\n", timestamp, owner)
	fmt.Printf("[%s] Repo: %s\n", timestamp, repo)
	fmt.Printf("[%s] Filepath: %s\n", timestamp, filepath)
	fmt.Printf("[%s] Ref: %s\n", timestamp, ref)
	fmt.Printf("[%s] Final URL: %s\n", timestamp, giteaURL)
	fmt.Printf("[%s] =============================\n", timestamp)

	req, err := http.NewRequest(http.MethodGet, giteaURL, nil)
	if err != nil {
		fmt.Printf("[ERROR] Failed to create request: %v\n", err)
		return nil, err
	}

	req.Header.Add("Authorization", "token "+c.token)
	fmt.Printf("[DEBUG] Request headers: %v\n", req.Header)

	// 创建一个跳过证书验证的 HTTP 客户端
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("[ERROR] HTTP request failed: %v\n", err)
		return nil, err
	}

	fmt.Printf("[DEBUG] Response status: %d %s\n", resp.StatusCode, resp.Status)
	fmt.Printf("[DEBUG] Response headers: %v\n", resp.Header)

	switch resp.StatusCode {
	case http.StatusNotFound:
		fmt.Printf("[ERROR] File not found (404): %s\n", giteaURL)
		return nil, fs.ErrNotExist
	case http.StatusOK:
		fmt.Printf("[DEBUG] File found successfully\n")
	default:
		fmt.Printf("[ERROR] Unexpected status code '%d' for URL: %s\n", resp.StatusCode, giteaURL)
		return nil, fmt.Errorf("unexpected status code '%d'", resp.StatusCode)
	}

	res, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("[ERROR] Failed to read response body: %v\n", err)
		return nil, err
	}

	defer resp.Body.Close()

	fmt.Printf("[DEBUG] Successfully read %d bytes from file\n", len(res))
	return res, nil
}

var bufPool = sync.Pool{
	New: func() any {
		return new(bytes.Buffer)
	},
}

func handleMD(res []byte) ([]byte, error) {
	meta, resbody, err := extractFrontMatter(string(res))
	if err != nil {
		return nil, err
	}

	resmd, err := markdown([]byte(resbody))
	if err != nil {
		return nil, err
	}

	title, _ := meta["title"].(string)

	res = append([]byte("<!DOCTYPE html>\n<html>\n<body>\n<h1>"), []byte(title)...)
	res = append(res, []byte("</h1>")...)
	res = append(res, resmd...)
	res = append(res, []byte("</body></html>")...)

	return res, nil
}

func (c *Client) repoTopics(owner, repo string) ([]string, error) {
	repos, _, err := c.gc.ListRepoTopics(owner, repo, gclient.ListRepoTopicsOptions{})
	return repos, err
}

func (c *Client) hasRepoBranch(owner, repo, branch string) bool {
	b, _, err := c.gc.GetRepoBranch(owner, repo, branch)
	if err != nil {
		return false
	}

	return b.Name == branch
}

func (c *Client) allowsPages(owner, repo string) (bool, bool) {
	topics, err := c.repoTopics(owner, repo)
	if err != nil {
		return false, false
	}

	for _, topic := range topics {
		if topic == c.giteapagesAllowAll {
			return true, true
		}
	}

	for _, topic := range topics {
		if topic == c.giteapages {
			return true, false
		}
	}

	return false, false
}

func (c *Client) readConfig(owner, repo string) error {
	cfg, err := c.getRawFileOrLFS(owner, repo, c.giteapages+".toml", c.giteapages)
	if err != nil {
		return err
	}

	viper.SetConfigType("toml")

	return viper.ReadConfig(bytes.NewBuffer(cfg))
}

func splitName(name string) (string, string, string) {
	parts := strings.Split(name, "/")

	// parts contains: ["owner", "repo", "filepath"]
	// 如果没有指定owner，默认使用QingXue
	switch len(parts) {
	case 1:
		// 只有一个部分，可能是owner或者filepath
		// 如果看起来像文件名（包含扩展名），则作为filepath处理
		if strings.Contains(parts[0], ".") {
			return "QingXue", "", parts[0]
		}
		return parts[0], "", ""
	case 2:
		// 两个部分，可能是owner/repo或repo/filepath
		// 如果第二部分看起来像文件名，则第一部分是repo
		if strings.Contains(parts[1], ".") {
			return "QingXue", parts[0], parts[1]
		}
		return parts[0], parts[1], ""
	default:
		return parts[0], parts[1], strings.Join(parts[2:], "/")
	}
}

func validRefs(ref string, allowall bool) bool {
	if allowall {
		return true
	}

	validrefs := viper.GetStringSlice("allowedrefs")
	for _, r := range validrefs {
		if r == ref {
			return true
		}

		if r == "*" {
			return true
		}
	}

	return false
}
