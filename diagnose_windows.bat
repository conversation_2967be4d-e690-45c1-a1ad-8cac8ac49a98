@echo off
echo === caddy-gitea-local 问题诊断 (Windows) ===

set GITEA_SERVER=http://***********:3000

REM 检查 curl 是否可用
where curl >nul 2>nul
if %errorlevel% neq 0 (
    echo 警告: curl 未找到，将使用 PowerShell 进行网络测试
    set USE_POWERSHELL=1
) else (
    set USE_POWERSHELL=0
)

echo.
echo 1. 检查 Gitea 服务器连接...
if %USE_POWERSHELL% equ 1 (
    powershell -Command "try { $response = Invoke-WebRequest -Uri '%GITEA_SERVER%' -TimeoutSec 5 -UseBasicParsing; Write-Host '✓ Gitea 服务器可访问' } catch { Write-Host '✗ Gitea 服务器不可访问' }"
) else (
    curl -s --connect-timeout 5 "%GITEA_SERVER%" >nul 2>nul
    if !errorlevel! equ 0 (
        echo ✓ Gitea 服务器 ^(%GITEA_SERVER%^) 可访问
    ) else (
        echo ✗ Gitea 服务器 ^(%GITEA_SERVER%^) 不可访问
        echo   请检查:
        echo   - Gitea 服务是否运行
        echo   - IP 地址和端口是否正确
        echo   - 网络连接是否正常
    )
)

echo.
echo 2. 检查 Gitea API...
set API_URL=%GITEA_SERVER%/api/v1/version
if %USE_POWERSHELL% equ 1 (
    powershell -Command "try { $response = Invoke-WebRequest -Uri '%API_URL%' -TimeoutSec 5 -UseBasicParsing; Write-Host '✓ Gitea API 可访问'; Write-Host '  版本信息:'; Write-Host $response.Content } catch { Write-Host '✗ Gitea API 不可访问' }"
) else (
    curl -s --connect-timeout 5 "%API_URL%" >nul 2>nul
    if !errorlevel! equ 0 (
        echo ✓ Gitea API 可访问
        echo   版本信息:
        curl -s "%API_URL%"
    ) else (
        echo ✗ Gitea API 不可访问
    )
)

echo.
echo 3. 检查目标仓库...
set REPO_URL=%GITEA_SERVER%/QingXue/gitea-pages
if %USE_POWERSHELL% equ 1 (
    powershell -Command "try { $response = Invoke-WebRequest -Uri '%REPO_URL%' -TimeoutSec 5 -UseBasicParsing; Write-Host '✓ 仓库 QingXue/gitea-pages 存在' } catch { Write-Host '✗ 仓库 QingXue/gitea-pages 不存在或不可访问' }"
) else (
    curl -s --connect-timeout 5 "%REPO_URL%" >nul 2>nul
    if !errorlevel! equ 0 (
        echo ✓ 仓库 QingXue/gitea-pages 存在
    ) else (
        echo ✗ 仓库 QingXue/gitea-pages 不存在或不可访问
        echo   请检查:
        echo   - 仓库是否已创建
        echo   - 仓库是否为公开仓库
        echo   - 用户名是否正确 ^(QingXue^)
    )
)

echo.
echo 4. 检查 gitea-pages 分支...
set BRANCH_URL=%GITEA_SERVER%/api/v1/repos/QingXue/gitea-pages/branches/gitea-pages
if %USE_POWERSHELL% equ 1 (
    powershell -Command "try { $response = Invoke-WebRequest -Uri '%BRANCH_URL%' -TimeoutSec 5 -UseBasicParsing; Write-Host '✓ gitea-pages 分支存在' } catch { Write-Host '✗ gitea-pages 分支不存在' }"
) else (
    curl -s --connect-timeout 5 "%BRANCH_URL%" >nul 2>nul
    if !errorlevel! equ 0 (
        echo ✓ gitea-pages 分支存在
    ) else (
        echo ✗ gitea-pages 分支不存在
        echo   请在仓库中创建 gitea-pages 分支
    )
)

echo.
echo 5. 检查仓库主题标签...
set TOPICS_URL=%GITEA_SERVER%/api/v1/repos/QingXue/gitea-pages/topics
if %USE_POWERSHELL% equ 1 (
    powershell -Command "try { $response = Invoke-WebRequest -Uri '%TOPICS_URL%' -TimeoutSec 5 -UseBasicParsing; Write-Host '✓ 可以访问主题标签 API'; if ($response.Content -like '*gitea-pages*') { Write-Host '✓ 找到 gitea-pages 主题标签' } else { Write-Host '✗ 未找到 gitea-pages 主题标签' } } catch { Write-Host '✗ 无法访问主题标签 API' }"
) else (
    curl -s --connect-timeout 5 "%TOPICS_URL%" >nul 2>nul
    if !errorlevel! equ 0 (
        echo ✓ 可以访问主题标签 API
        REM 简单检查是否包含 gitea-pages
        curl -s "%TOPICS_URL%" | findstr "gitea-pages" >nul
        if !errorlevel! equ 0 (
            echo ✓ 找到 gitea-pages 主题标签
        ) else (
            echo ✗ 未找到 gitea-pages 主题标签
            echo   请为仓库添加 'gitea-pages' 主题标签
        )
    ) else (
        echo ✗ 无法访问主题标签 API
    )
)

echo.
echo === 诊断完成 ===
echo.
echo 如果所有检查都通过，但仍然出现 'file does not exist' 错误，请检查:
echo 1. Gitea token 是否有效且有读取权限
echo 2. gitea-pages 分支中是否有 index.html 文件
echo 3. Caddy 配置中的 server URL 是否正确
echo.
pause
