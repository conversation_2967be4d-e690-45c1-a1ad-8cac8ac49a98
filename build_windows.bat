@echo off
echo === 构建 caddy-gitea-local (Windows) ===

REM 检查 Go 是否安装
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: Go 未安装或不在 PATH 中
    echo 请从 https://golang.org/dl/ 下载并安装 Go
    pause
    exit /b 1
)

REM 显示 Go 版本
echo 检测到 Go 版本:
go version

REM 清理模块依赖
echo 清理模块依赖...
go mod tidy
if %errorlevel% neq 0 (
    echo 错误: go mod tidy 失败
    pause
    exit /b 1
)

REM 检查语法
echo 检查代码语法...
go vet ./...
if %errorlevel% neq 0 (
    echo 错误: 代码语法检查失败
    pause
    exit /b 1
)

REM 尝试编译
echo 尝试编译...
go build -o test-build.exe ./...
if %errorlevel% equ 0 (
    echo ✓ 编译成功！
    del test-build.exe
) else (
    echo ✗ 编译失败
    pause
    exit /b 1
)

REM 检查 xcaddy 是否安装
where xcaddy >nul 2>nul
if %errorlevel% neq 0 (
    echo 警告: xcaddy 未安装。正在安装...
    go install github.com/caddyserver/xcaddy/cmd/xcaddy@latest
    if %errorlevel% neq 0 (
        echo 错误: xcaddy 安装失败
        pause
        exit /b 1
    )
)

REM 构建 Caddy
echo 开始构建 Caddy...
xcaddy build --with caddy-gitea-local=./
if %errorlevel% equ 0 (
    echo ✓ Caddy 构建成功！
    echo.
    echo 生成的文件: caddy.exe
    echo.
    echo 使用方法:
    echo 1. 复制 Caddyfile.example 为 Caddyfile
    echo 2. 修改 Caddyfile 中的 token
    echo 3. 运行: caddy.exe run
    echo.
    echo 或使用调试模式:
    echo caddy.exe run --config Caddyfile.debug
) else (
    echo ✗ Caddy 构建失败
    pause
    exit /b 1
)

echo.
echo 构建完成！
pause
