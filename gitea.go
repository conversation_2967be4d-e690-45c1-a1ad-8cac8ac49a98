package gitea

import (
	"io"
	"net/http"
	"strings"

	"caddy-gitea-local/pkg/gitea"
	"github.com/caddyserver/caddy/v2"
	"github.com/caddyserver/caddy/v2/caddyconfig/caddyfile"
	"github.com/caddyserver/caddy/v2/caddyconfig/httpcaddyfile"
	"github.com/caddyserver/caddy/v2/modules/caddyhttp"
	"go.uber.org/zap"
)

const Version = "v1.0.0-local"

func init() {
	caddy.RegisterModule(Middleware{})
	httpcaddyfile.RegisterHandlerDirective("gitea", parseCaddyfile)
}

func parseCaddyfile(h httpcaddyfile.Helper) (caddyhttp.MiddlewareHandler, error) {
	var m Middleware
	err := m.UnmarshalCaddyfile(h.Dispenser)

	return m, err
}

// Middleware implements gitea plugin.
type Middleware struct {
	Client             *gitea.Client `json:"-"`
	Server             string        `json:"server,omitempty"`
	Token              string        `json:"token,omitempty"`
	GiteaPages         string        `json:"gitea_pages,omitempty"`
	GiteaPagesAllowAll string        `json:"gitea_pages_allowall,omitempty"`
	Domain             string        `json:"domain,omitempty"` // 保留但不再必需
}

// CaddyModule returns the Caddy module information.
func (Middleware) CaddyModule() caddy.ModuleInfo {
	return caddy.ModuleInfo{
		ID:  "http.handlers.gitea",
		New: func() caddy.Module { return new(Middleware) },
	}
}

// Provision provisions gitea client.
func (m *Middleware) Provision(ctx caddy.Context) error {
	logger := ctx.Logger(m)
	logger.Info("Provisioning caddy-gitea-local plugin",
		zap.String("version", Version),
		zap.String("server", m.Server),
		zap.String("gitea_pages", m.GiteaPages),
		zap.String("gitea_pages_allowall", m.GiteaPagesAllowAll))

	var err error
	m.Client, err = gitea.NewClient(m.Server, m.Token, m.GiteaPages, m.GiteaPagesAllowAll)
	if err != nil {
		logger.Error("Failed to create gitea client", zap.Error(err))
		return err
	}

	logger.Info("Gitea client created successfully")
	return nil
}

// Validate implements caddy.Validator.
func (m *Middleware) Validate() error {
	return nil
}

// UnmarshalCaddyfile unmarshals a Caddyfile.
func (m *Middleware) UnmarshalCaddyfile(d *caddyfile.Dispenser) error {
	for d.Next() {
		for n := d.Nesting(); d.NextBlock(n); {
			switch d.Val() {
			case "server":
				d.Args(&m.Server)
			case "token":
				d.Args(&m.Token)
			case "gitea_pages":
				d.Args(&m.GiteaPages)
			case "gitea_pages_allowall":
				d.Args(&m.GiteaPagesAllowAll)
			case "domain":
				d.Args(&m.Domain)
			}
		}
	}

	return nil
}

// ServeHTTP performs gitea content fetcher.
func (m Middleware) ServeHTTP(w http.ResponseWriter, r *http.Request, next caddyhttp.Handler) error {
	// 获取logger
	logger := caddy.Log().Named("gitea")

	// 简化的URL解析逻辑，直接使用路径
	// 默认使用 QingXue/gitea-pages 作为 owner/repo
	// URL格式: /filepath 或 /repo/filepath

	path := strings.TrimPrefix(r.URL.Path, "/")
	ref := r.URL.Query().Get("ref")

	logger.Debug("Processing request",
		zap.String("path", path),
		zap.String("ref", ref),
		zap.String("host", r.Host),
		zap.String("method", r.Method))

	var fp string

	// 如果路径为空，默认访问 QingXue/gitea-pages/index.html
	if path == "" {
		fp = "QingXue/gitea-pages/index.html"
	} else {
		// 直接使用完整路径，格式: repo/filepath 或 repo/folder/filepath
		// 例如: OcrLibrary/OcrLibrary/README.md -> QingXue/OcrLibrary/OcrLibrary/README.md
		fp = "QingXue/" + path
	}

	// 如果没有指定ref，默认使用gitea-pages分支
	if ref == "" {
		ref = "gitea-pages"
	}

	logger.Debug("Resolved file path",
		zap.String("filepath", fp),
		zap.String("ref", ref))

	f, err := m.Client.Open(fp, ref)
	if err != nil {
		logger.Error("Failed to open file",
			zap.String("filepath", fp),
			zap.String("ref", ref),
			zap.Error(err))
		return caddyhttp.Error(http.StatusNotFound, err)
	}

	logger.Debug("File opened successfully", zap.String("filepath", fp))

	_, err = io.Copy(w, f)
	if err != nil {
		logger.Error("Failed to copy file content", zap.Error(err))
	}

	return err
}

// Interface guards
var (
	_ caddy.Provisioner           = (*Middleware)(nil)
	_ caddy.Validator             = (*Middleware)(nil)
	_ caddyhttp.MiddlewareHandler = (*Middleware)(nil)
	_ caddyfile.Unmarshaler       = (*Middleware)(nil)
)
