# caddy-gitea-local

本地化的 [Gitea](https://gitea.io) 插件，适用于 [Caddy v2](https://github.com/caddyserver/caddy)。

这个版本简化了配置，支持直接通过IP地址和端口访问，无需配置域名。
默认使用 QingXue 作为 owner，gitea-pages 作为默认仓库和分支。

Markdown 文件（`.md` 扩展名）会自动转换为 HTML。

<!-- TOC -->

- [caddy-gitea-local](#caddy-gitea-local)
    - [快速开始](#快速开始)
        - [Caddy 配置](#caddy-配置)
        - [访问方式](#访问方式)
        - [Gitea 配置](#gitea-配置)
    - [构建 Caddy](#构建-caddy)

<!-- /TOC -->

## 快速开始

### Caddy 配置

下面的 Caddyfile 创建一个监听 :8098 端口的 Web 服务器，它将与你的 Gitea 服务器交互。
你需要一个具有相应仓库读取权限的 Gitea token。

```Caddyfile
{
        order gitea before file_server
}
:8098
gitea {
        server http://***********:3000
        token your_gitea_token
}
```

### 访问方式

简化后的访问方式，直接通过 IP 地址和端口访问：

- `http://***********:8098/` - 访问 QingXue/gitea-pages 仓库的 index.html
- `http://***********:8098/file.html` - 访问 QingXue/gitea-pages 仓库的 file.html
- `http://***********:8098/repo/file.html` - 访问 QingXue/repo 仓库的 file.html
- `http://***********:8098/file.html?ref=branch` - 访问指定分支的文件（默认为 gitea-pages 分支）

### Gitea 配置

你需要在 QingXue 用户下配置仓库以启用页面功能：

#### 默认 gitea-pages 仓库

1. 在 QingXue 用户下创建 `gitea-pages` 仓库
2. 为这个 `gitea-pages` 仓库添加 `gitea-pages` 主题标签（用于启用页面功能）
3. 在这个 `gitea-pages` 仓库中创建 `gitea-pages` 分支
4. 将你的内容放在这个分支中（例如 index.html, file.html 等）

你的内容现在可以通过 `http://***********:8098/file.html` 访问

#### 其他仓库配置

对于 QingXue 用户下的其他仓库：

1. 为仓库添加 `gitea-pages` 主题标签来启用页面功能
2. 在仓库中创建 `gitea-pages` 分支
3. 在 `gitea-pages` 分支中放置 `gitea-pages.toml` 配置文件

`gitea-pages.toml` 文件示例：

```toml
# 允许所有分支
allowedrefs=["*"]

# 或者只允许特定分支
allowedrefs=["main","dev","gitea-pages"]
```

#### 简化配置（推荐）

为了最简单的配置，你可以为仓库添加 `gitea-pages-allowall` 主题标签，这样所有分支都会被暴露（安全性较低但配置简单）。

## 构建 Caddy

由于这是一个本地化的第三方插件，你需要自己构建 Caddy。
构建需要安装 Go 1.19 或更高版本。

```bash
# 安装 xcaddy
go install github.com/caddyserver/xcaddy/cmd/xcaddy@latest

# 在项目目录下构建 Caddy（使用本地模块）
xcaddy build --with caddy-gitea-local=./
```

构建完成后，你会得到一个包含此插件的 `caddy` 可执行文件。
