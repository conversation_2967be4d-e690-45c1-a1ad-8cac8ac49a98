# 多阶段构建 Dockerfile for caddy-gitea-local
FROM caddy:2.6-builder-alpine AS builder

# 复制本地源码到构建容器
COPY . /src/caddy-gitea-local

# 设置工作目录
WORKDIR /src/caddy-gitea-local

# 构建 Caddy with 本地插件
RUN xcaddy build \
    --with caddy-gitea-local=/src/caddy-gitea-local

FROM caddy:2.6.2

# 复制构建好的 caddy 二进制文件
COPY --from=builder /src/caddy-gitea-local/caddy /usr/bin/caddy

# 添加版本标签
LABEL version="1.0.0-local"
LABEL description="Caddy with caddy-gitea-local plugin"
