#!/bin/bash

# Docker 构建脚本
set -e

echo "=== 构建 caddy-gitea-local Docker 镜像 ==="

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker 未安装。请先安装 Docker。"
    exit 1
fi

# 构建镜像
echo "开始构建 Docker 镜像..."
docker build -t caddy-gitea-local:latest .

if [ $? -eq 0 ]; then
    echo "✓ Docker 镜像构建成功！"
    
    # 显示镜像信息
    echo ""
    echo "镜像信息:"
    docker images caddy-gitea-local:latest
    
    echo ""
    echo "使用方法:"
    echo "1. 创建 Caddyfile 配置文件"
    echo "2. 运行容器:"
    echo "   docker run -d -p 8098:8098 -v \$(pwd)/Caddyfile:/etc/caddy/Caddyfile caddy-gitea-local:latest"
    echo ""
    echo "或者使用调试模式:"
    echo "   docker run -d -p 8098:8098 -v \$(pwd)/Caddyfile.debug:/etc/caddy/Caddyfile caddy-gitea-local:latest"
else
    echo "✗ Docker 镜像构建失败"
    exit 1
fi
