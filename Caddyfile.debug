{
	order gitea before file_server
	# 启用调试日志
	debug
}

:8098 {
	gitea {
		server http://192.168.3.4:3000
		token your_gitea_token_here
		gitea_pages gitea-pages
		gitea_pages_allowall gitea-pages-allowall
	}
	
	# 详细的日志记录
	log {
		output file /var/log/caddy/gitea-pages.log {
			roll_size 10MB
			roll_keep 5
		}
		format json
		level DEBUG
	}
	
	# 添加错误处理页面
	handle_errors {
		respond "Error {http.error.status_code}: {http.error.status_text}"
	}
}
