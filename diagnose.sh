#!/bin/bash

# 问题诊断脚本
echo "=== caddy-gitea-local 问题诊断 ==="

# 检查 Gitea 服务器连接
GITEA_SERVER="http://***********:3000"
echo "1. 检查 Gitea 服务器连接..."
if curl -s --connect-timeout 5 "$GITEA_SERVER" > /dev/null; then
    echo "✓ Gitea 服务器 ($GITEA_SERVER) 可访问"
else
    echo "✗ Gitea 服务器 ($GITEA_SERVER) 不可访问"
    echo "  请检查:"
    echo "  - Gitea 服务是否运行"
    echo "  - IP 地址和端口是否正确"
    echo "  - 网络连接是否正常"
fi

# 检查 Gitea API
echo ""
echo "2. 检查 Gitea API..."
API_URL="$GITEA_SERVER/api/v1/version"
if curl -s --connect-timeout 5 "$API_URL" > /dev/null; then
    echo "✓ Gitea API 可访问"
    echo "  版本信息:"
    curl -s "$API_URL" | grep -o '"version":"[^"]*"' || echo "  无法获取版本信息"
else
    echo "✗ Gitea API 不可访问"
fi

# 检查仓库是否存在
echo ""
echo "3. 检查目标仓库..."
REPO_URL="$GITEA_SERVER/QingXue/gitea-pages"
if curl -s --connect-timeout 5 "$REPO_URL" > /dev/null; then
    echo "✓ 仓库 QingXue/gitea-pages 存在"
else
    echo "✗ 仓库 QingXue/gitea-pages 不存在或不可访问"
    echo "  请检查:"
    echo "  - 仓库是否已创建"
    echo "  - 仓库是否为公开仓库"
    echo "  - 用户名是否正确 (QingXue)"
fi

# 检查分支
echo ""
echo "4. 检查 gitea-pages 分支..."
BRANCH_URL="$GITEA_SERVER/api/v1/repos/QingXue/gitea-pages/branches/gitea-pages"
if curl -s --connect-timeout 5 "$BRANCH_URL" > /dev/null; then
    echo "✓ gitea-pages 分支存在"
else
    echo "✗ gitea-pages 分支不存在"
    echo "  请在仓库中创建 gitea-pages 分支"
fi

# 检查主题标签
echo ""
echo "5. 检查仓库主题标签..."
TOPICS_URL="$GITEA_SERVER/api/v1/repos/QingXue/gitea-pages/topics"
if curl -s --connect-timeout 5 "$TOPICS_URL" > /dev/null; then
    echo "✓ 可以访问主题标签 API"
    TOPICS=$(curl -s "$TOPICS_URL" 2>/dev/null)
    if echo "$TOPICS" | grep -q "gitea-pages"; then
        echo "✓ 找到 gitea-pages 主题标签"
    else
        echo "✗ 未找到 gitea-pages 主题标签"
        echo "  请为仓库添加 'gitea-pages' 主题标签"
    fi
else
    echo "✗ 无法访问主题标签 API"
fi

echo ""
echo "=== 诊断完成 ==="
echo ""
echo "如果所有检查都通过，但仍然出现 'file does not exist' 错误，请检查:"
echo "1. Gitea token 是否有效且有读取权限"
echo "2. gitea-pages 分支中是否有 index.html 文件"
echo "3. Caddy 配置中的 server URL 是否正确"
