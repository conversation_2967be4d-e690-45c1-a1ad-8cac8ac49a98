# 使用说明

## 修改内容总结

本次修改简化了 caddy-gitea 插件的使用方式，主要变更包括：

### 1. 模块路径修改
- 将模块路径从 `github.com/42wim/caddy-gitea` 改为 `caddy-gitea-local`
- 更新了相关的 import 路径

### 2. URL 解析逻辑简化
- 移除了复杂的域名解析逻辑
- 支持直接通过 IP 地址和端口访问
- 默认使用 QingXue 作为 owner
- 默认使用 gitea-pages 作为仓库名和分支名

### 3. 访问方式
现在支持以下访问方式：

```
http://***********:8098/                    # QingXue/gitea-pages/index.html
http://***********:8098/file.html           # QingXue/gitea-pages/file.html  
http://***********:8098/repo/file.html      # QingXue/repo/file.html
http://***********:8098/file.html?ref=dev   # 指定分支访问
```

### 4. 配置简化
Caddyfile 配置示例：

```caddyfile
{
    order gitea before file_server
}

:8098 {
    gitea {
        server http://***********:3000
        token your_gitea_token
    }
}
```

## 部署步骤

### 1. 准备 Gitea 仓库
在你的 Gitea 服务器上，确保 QingXue 用户下有：
- `gitea-pages` 仓库
- `gitea-pages` 分支
- 为仓库添加 `gitea-pages` 主题标签

### 2. 构建 Caddy
```bash
# 安装 xcaddy（如果还没安装）
go install github.com/caddyserver/xcaddy/cmd/xcaddy@latest

# 在项目目录下构建
xcaddy build --with caddy-gitea-local=./
```

### 3. 配置和运行
1. 复制 `Caddyfile.example` 为 `Caddyfile`
2. 修改其中的 token 为你的 Gitea token
3. 运行 Caddy：`./caddy run`

### 4. 测试访问
访问 `http://***********:8098/` 应该能看到你的页面内容。

## 注意事项

1. **安全性**: 这个修改移除了域名验证，直接暴露在指定端口上。确保你的网络环境安全。

2. **Token 权限**: Gitea token 需要有对应仓库的读取权限。

3. **分支配置**: 默认使用 `gitea-pages` 分支，可以通过 `?ref=branch_name` 参数访问其他分支。

4. **文件类型**: Markdown 文件会自动转换为 HTML 显示。

## 故障排除

### 404 错误
- 检查 QingXue 用户是否存在
- 检查 gitea-pages 仓库是否存在
- 检查仓库是否有 gitea-pages 主题标签
- 检查 gitea-pages 分支是否存在

### 权限错误
- 检查 Gitea token 是否有效
- 检查 token 是否有仓库读取权限

### 连接错误
- 检查 Gitea 服务器地址是否正确
- 检查网络连接是否正常
