{
	order gitea before file_server
	debug
	# 禁用自动 HTTPS，因为我们使用 IP 地址
	auto_https off
}

# 监听 8098 端口，不使用 TLS
***********:8098 {
	gitea {
		server https://***********:33000
		token your_actual_token_here
		gitea_pages gitea-pages
		gitea_pages_allowall gitea-pages-allowall
	}
	
	# 详细的日志记录到控制台
	log {
		output stdout
		format console
		level DEBUG
	}
	
	# 添加错误处理页面
	handle_errors {
		respond "Error {http.error.status_code}: {http.error.status_text} - Path: {http.request.uri.path} - Host: {http.request.host}"
	}
}

# 或者如果你想监听所有接口的 8098 端口
:8098 {
	gitea {
		server https://***********:33000
		token your_actual_token_here
		gitea_pages gitea-pages
		gitea_pages_allowall gitea-pages-allowall
	}
	
	# 详细的日志记录到控制台
	log {
		output stdout
		format console
		level DEBUG
	}
	
	# 添加错误处理页面
	handle_errors {
		respond "Error {http.error.status_code}: {http.error.status_text} - Path: {http.request.uri.path} - Host: {http.request.host}"
	}
}
