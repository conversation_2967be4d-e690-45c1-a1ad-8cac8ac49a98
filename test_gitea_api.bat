@echo off
setlocal enabledelayedexpansion

REM 测试 Gitea API 连接脚本
set GITEA_SERVER=https://***********:33000
set TOKEN=your_token_here

echo === 测试 Gitea API 连接 ===
echo 服务器: %GITEA_SERVER%
echo.

REM 检查 PowerShell 是否可用
where powershell >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: PowerShell 未找到
    pause
    exit /b 1
)

REM 1. 测试基本连接
echo 1. 测试基本连接...
powershell -Command "try { $response = Invoke-WebRequest -Uri '%GITEA_SERVER%' -TimeoutSec 10 -SkipCertificateCheck -UseBasicParsing; Write-Host 'OK 基本连接成功' } catch { Write-Host 'ERROR 基本连接失败:' $_.Exception.Message }"

REM 2. 测试 API 版本
echo.
echo 2. 测试 API 版本...
powershell -Command "try { $response = Invoke-WebRequest -Uri '%GITEA_SERVER%/api/v1/version' -TimeoutSec 10 -SkipCertificateCheck -UseBasicParsing; Write-Host 'OK API 可访问'; Write-Host '版本信息:' $response.Content } catch { Write-Host 'ERROR API 不可访问:' $_.Exception.Message }"

REM 3. 检查 Token
echo.
echo 3. 检查 Token...
if "%TOKEN%"=="your_token_here" (
    echo WARNING 请先设置正确的 TOKEN
    echo 编辑此脚本，将 TOKEN 变量设置为你的实际 token
    pause
    exit /b 1
)

REM 4. 测试 Token 认证
echo.
echo 4. 测试 Token 认证...
powershell -Command "$headers = @{Authorization = 'token %TOKEN%'}; try { $response = Invoke-WebRequest -Uri '%GITEA_SERVER%/api/v1/user' -Headers $headers -TimeoutSec 10 -SkipCertificateCheck -UseBasicParsing; $user = $response.Content | ConvertFrom-Json; Write-Host 'OK Token 认证成功'; Write-Host '当前用户:' $user.login } catch { Write-Host 'ERROR Token 认证失败:' $_.Exception.Message }"

REM 5. 测试仓库访问
echo.
echo 5. 测试仓库访问...
powershell -Command "$headers = @{Authorization = 'token %TOKEN%'}; try { $response = Invoke-WebRequest -Uri '%GITEA_SERVER%/api/v1/repos/QingXue/gitea-pages' -Headers $headers -TimeoutSec 10 -SkipCertificateCheck -UseBasicParsing; $repo = $response.Content | ConvertFrom-Json; Write-Host 'OK 仓库 QingXue/gitea-pages 可访问'; Write-Host '仓库名称:' $repo.name } catch { Write-Host 'ERROR 仓库 QingXue/gitea-pages 不可访问:' $_.Exception.Message }"

REM 6. 测试分支
echo.
echo 6. 测试 gitea-pages 分支...
powershell -Command "$headers = @{Authorization = 'token %TOKEN%'}; try { $response = Invoke-WebRequest -Uri '%GITEA_SERVER%/api/v1/repos/QingXue/gitea-pages/branches/gitea-pages' -Headers $headers -TimeoutSec 10 -SkipCertificateCheck -UseBasicParsing; $branch = $response.Content | ConvertFrom-Json; Write-Host 'OK gitea-pages 分支存在'; Write-Host '分支名称:' $branch.name } catch { Write-Host 'ERROR gitea-pages 分支不存在:' $_.Exception.Message }"

REM 7. 测试主题标签
echo.
echo 7. 测试仓库主题标签...
powershell -Command "$headers = @{Authorization = 'token %TOKEN%'}; try { $response = Invoke-WebRequest -Uri '%GITEA_SERVER%/api/v1/repos/QingXue/gitea-pages/topics' -Headers $headers -TimeoutSec 10 -SkipCertificateCheck -UseBasicParsing; $topics = $response.Content | ConvertFrom-Json; Write-Host '主题标签:' ($topics.topics -join ', '); if ($topics.topics -contains 'gitea-pages') { Write-Host 'OK 找到 gitea-pages 主题标签' } else { Write-Host 'WARNING 未找到 gitea-pages 主题标签，建议添加' } } catch { Write-Host 'ERROR 无法访问主题标签 API:' $_.Exception.Message }"

REM 8. 测试文件访问
echo.
echo 8. 测试文件访问...
powershell -Command "$headers = @{Authorization = 'token %TOKEN%'}; try { $response = Invoke-WebRequest -Uri '%GITEA_SERVER%/api/v1/repos/QingXue/gitea-pages/media/index.html?ref=gitea-pages' -Headers $headers -TimeoutSec 10 -SkipCertificateCheck -UseBasicParsing; Write-Host 'OK index.html 文件可访问'; Write-Host '文件大小:' $response.Content.Length '字节' } catch { Write-Host 'ERROR index.html 文件不可访问:' $_.Exception.Message }"

echo.
echo === 测试完成 ===
echo.
echo 如果所有测试都通过，请重新构建并运行 Caddy:
echo 1. 运行 build_windows.bat 重新构建
echo 2. 使用正确的 token 更新 Caddyfile
echo 3. 运行 caddy.exe run --config Caddyfile.debug
echo.
pause
