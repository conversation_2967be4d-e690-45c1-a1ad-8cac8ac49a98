@echo off
echo === 快速测试 Gitea 连接 ===

set GITEA_SERVER=https://***********:33000
set /p TOKEN="请输入你的 Gitea Token: "

echo.
echo 测试连接到: %GITEA_SERVER%
echo.

REM 测试基本连接
echo 1. 测试基本连接...
powershell -Command "try { Invoke-WebRequest -Uri '%GITEA_SERVER%' -TimeoutSec 5 -SkipCertificateCheck -UseBasicParsing | Out-Null; Write-Host 'OK - 服务器可访问' } catch { Write-Host 'ERROR - 服务器不可访问:' $_.Exception.Message }"

REM 测试 API
echo.
echo 2. 测试 API...
powershell -Command "try { $response = Invoke-RestMethod -Uri '%GITEA_SERVER%/api/v1/version' -TimeoutSec 5 -SkipCertificateCheck; Write-Host 'OK - API 可访问, 版本:' $response.version } catch { Write-Host 'ERROR - API 不可访问:' $_.Exception.Message }"

REM 测试 Token
echo.
echo 3. 测试 Token...
powershell -Command "$headers = @{Authorization = 'token %TOKEN%'}; try { $user = Invoke-RestMethod -Uri '%GITEA_SERVER%/api/v1/user' -Headers $headers -TimeoutSec 5 -SkipCertificateCheck; Write-Host 'OK - Token 有效, 用户:' $user.login } catch { Write-Host 'ERROR - Token 无效:' $_.Exception.Message }"

REM 测试仓库
echo.
echo 4. 测试仓库...
powershell -Command "$headers = @{Authorization = 'token %TOKEN%'}; try { $repo = Invoke-RestMethod -Uri '%GITEA_SERVER%/api/v1/repos/QingXue/gitea-pages' -Headers $headers -TimeoutSec 5 -SkipCertificateCheck; Write-Host 'OK - 仓库可访问:' $repo.name } catch { Write-Host 'ERROR - 仓库不可访问:' $_.Exception.Message }"

REM 测试分支
echo.
echo 5. 测试分支...
powershell -Command "$headers = @{Authorization = 'token %TOKEN%'}; try { $branch = Invoke-RestMethod -Uri '%GITEA_SERVER%/api/v1/repos/QingXue/gitea-pages/branches/gitea-pages' -Headers $headers -TimeoutSec 5 -SkipCertificateCheck; Write-Host 'OK - 分支存在:' $branch.name } catch { Write-Host 'ERROR - 分支不存在:' $_.Exception.Message }"

REM 测试文件
echo.
echo 6. 测试文件...
powershell -Command "$headers = @{Authorization = 'token %TOKEN%'}; try { $response = Invoke-WebRequest -Uri '%GITEA_SERVER%/api/v1/repos/QingXue/gitea-pages/media/index.html?ref=gitea-pages' -Headers $headers -TimeoutSec 5 -SkipCertificateCheck -UseBasicParsing; Write-Host 'OK - 文件可访问, 大小:' $response.Content.Length '字节' } catch { Write-Host 'ERROR - 文件不可访问:' $_.Exception.Message }"

echo.
echo === 测试完成 ===
pause
