@echo off
echo === 快速重新构建 caddy-gitea-local ===

REM 检查是否有 Go
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: Go 未安装
    pause
    exit /b 1
)

REM 检查是否有 xcaddy
where xcaddy >nul 2>nul
if %errorlevel% neq 0 (
    echo 安装 xcaddy...
    go install github.com/caddyserver/xcaddy/cmd/xcaddy@latest
)

echo 重新构建 Caddy...
xcaddy build --with caddy-gitea-local=./

if %errorlevel% equ 0 (
    echo ✓ 构建成功！
    echo.
    echo 现在可以运行:
    echo caddy.exe run --config Caddyfile.fixed
    echo.
    echo 记得先在 Caddyfile.fixed 中设置正确的 token！
) else (
    echo ✗ 构建失败
)

pause
