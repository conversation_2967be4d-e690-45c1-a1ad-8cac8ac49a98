#!/bin/bash

# 测试 Gitea API 连接脚本
GITEA_SERVER="https://192.168.3.4:33000"
TOKEN="your_token_here"  # 请替换为实际的 token

echo "=== 测试 Gitea API 连接 ==="
echo "服务器: $GITEA_SERVER"
echo

# 1. 测试基本连接
echo "1. 测试基本连接..."
curl -k -s --connect-timeout 10 "$GITEA_SERVER" > /dev/null
if [ $? -eq 0 ]; then
    echo "✓ 基本连接成功"
else
    echo "✗ 基本连接失败"
    echo "请检查:"
    echo "- Gitea 服务是否在 $GITEA_SERVER 运行"
    echo "- 网络连接是否正常"
    echo "- 如果使用 HTTPS，检查证书是否有效"
    exit 1
fi

# 2. 测试 API 版本
echo
echo "2. 测试 API 版本..."
API_VERSION=$(curl -k -s --connect-timeout 10 "$GITEA_SERVER/api/v1/version")
if [ $? -eq 0 ] && [ -n "$API_VERSION" ]; then
    echo "✓ API 可访问"
    echo "版本信息: $API_VERSION"
else
    echo "✗ API 不可访问"
    exit 1
fi

# 3. 测试 Token 认证
echo
echo "3. 测试 Token 认证..."
if [ "$TOKEN" = "your_token_here" ]; then
    echo "⚠ 请先设置正确的 TOKEN"
    echo "编辑此脚本，将 TOKEN 变量设置为你的实际 token"
    exit 1
fi

USER_INFO=$(curl -k -s --connect-timeout 10 -H "Authorization: token $TOKEN" "$GITEA_SERVER/api/v1/user")
if echo "$USER_INFO" | grep -q "login"; then
    echo "✓ Token 认证成功"
    USERNAME=$(echo "$USER_INFO" | grep -o '"login":"[^"]*"' | cut -d'"' -f4)
    echo "当前用户: $USERNAME"
else
    echo "✗ Token 认证失败"
    echo "响应: $USER_INFO"
    exit 1
fi

# 4. 测试仓库访问
echo
echo "4. 测试仓库访问..."
REPO_INFO=$(curl -k -s --connect-timeout 10 -H "Authorization: token $TOKEN" "$GITEA_SERVER/api/v1/repos/QingXue/gitea-pages")
if echo "$REPO_INFO" | grep -q '"name":"gitea-pages"'; then
    echo "✓ 仓库 QingXue/gitea-pages 可访问"
else
    echo "✗ 仓库 QingXue/gitea-pages 不可访问"
    echo "响应: $REPO_INFO"
    echo "请检查:"
    echo "- 仓库是否存在"
    echo "- Token 是否有读取权限"
    echo "- 用户名是否正确"
fi

# 5. 测试分支
echo
echo "5. 测试 gitea-pages 分支..."
BRANCH_INFO=$(curl -k -s --connect-timeout 10 -H "Authorization: token $TOKEN" "$GITEA_SERVER/api/v1/repos/QingXue/gitea-pages/branches/gitea-pages")
if echo "$BRANCH_INFO" | grep -q '"name":"gitea-pages"'; then
    echo "✓ gitea-pages 分支存在"
else
    echo "✗ gitea-pages 分支不存在"
    echo "响应: $BRANCH_INFO"
fi

# 6. 测试主题标签
echo
echo "6. 测试仓库主题标签..."
TOPICS=$(curl -k -s --connect-timeout 10 -H "Authorization: token $TOKEN" "$GITEA_SERVER/api/v1/repos/QingXue/gitea-pages/topics")
echo "主题标签: $TOPICS"
if echo "$TOPICS" | grep -q "gitea-pages"; then
    echo "✓ 找到 gitea-pages 主题标签"
else
    echo "⚠ 未找到 gitea-pages 主题标签"
    echo "建议为仓库添加 'gitea-pages' 主题标签"
fi

# 7. 测试文件访问
echo
echo "7. 测试文件访问..."
FILE_URL="$GITEA_SERVER/api/v1/repos/QingXue/gitea-pages/media/index.html?ref=gitea-pages"
FILE_CONTENT=$(curl -k -s --connect-timeout 10 -H "Authorization: token $TOKEN" "$FILE_URL")
if [ $? -eq 0 ] && [ -n "$FILE_CONTENT" ] && ! echo "$FILE_CONTENT" | grep -q "message"; then
    echo "✓ index.html 文件可访问"
    echo "文件大小: $(echo -n "$FILE_CONTENT" | wc -c) 字节"
else
    echo "✗ index.html 文件不可访问"
    echo "响应: $FILE_CONTENT"
fi

echo
echo "=== 测试完成 ==="
