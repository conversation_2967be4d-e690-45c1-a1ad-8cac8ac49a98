{
	order gitea before file_server
	debug
}

:8098 {
	gitea {
		server http://192.168.3.4:3000
		token ghp_your_token_here_replace_this
		gitea_pages gitea-pages
		gitea_pages_allowall gitea-pages-allowall
	}
	
	# 详细的日志记录到控制台
	log {
		output stdout
		format console
		level DEBUG
	}
	
	# 添加错误处理页面
	handle_errors {
		respond "Error {http.error.status_code}: {http.error.status_text} - Path: {http.request.uri.path}"
	}
}
